const history = require("../models/plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
  const AppointmentGuestNdaAgreement = sequelize.define(
    "AppointmentGuestNdaAgreement",
    {
      appointment_guest_nda_agreement_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      appointment_guest_id: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      signed_at: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      signer_role: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      nda_template_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "nda_template",
          key: "nda_template_id",
        },
        onDelete: "CASCADE",
      },
      effective_date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      expiration_date: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "appointment_guest_nda_agreement",
      timestamps: true,
      underscored: true,
    }
  );

  AppointmentGuestNdaAgreement.associate = (models) => {
    AppointmentGuestNdaAgreement.belongsTo(models.AppointmentGuest, {
      foreignKey: "appointment_guest_id",
      as: "appointmentGuest",
    });

    AppointmentGuestNdaAgreement.belongsTo(models.NdaTemplate, {
      foreignKey: "nda_template_id",
      as: "template",
    });

    AppointmentGuestNdaAgreement.belongsTo(models.MasterData, {
      foreignKey: "status",
      targetKey: "key",
      as: "nda_agreement_status_name",
      constraints: false,
      scope: {
        group: "nda_agreement_status",
      },
    });

    AppointmentGuestNdaAgreement.belongsTo(models.MasterData, {
      foreignKey: "signer_role",
      targetKey: "key",
      as: "signer_role_name",
      constraints: false,
      scope: {
        group: "signer_role",
      },
    });

    AppointmentGuestNdaAgreement.hasMany(models.PatientNdaSignature, {
      foreignKey: "appointment_guest_nda_agreement_id",
      as: "signatures",
    });
  };

  history(AppointmentGuestNdaAgreement, sequelize, DataTypes);

  return AppointmentGuestNdaAgreement;
};
