const history = require("../models/plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
  const AppointmentGuestPatientNdaAgreement = sequelize.define(
    "AppointmentGuestPatientNdaAgreement",
    {
      appointment_guest_patient_nda_agreement_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      appointment_guest_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "appointment_guest",
          key: "appointment_guest_id",
        },
        onDelete: "CASCADE",
      },
      patient_nda_agreement_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "nda_agreement",
          key: "patient_nda_agreement_id",
        },
        onDelete: "CASCADE",
      }},
    {
      tableName: "appointment_guest_nda_agreement",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          unique: true,
          fields: ["appointment_guest_id", "patient_nda_agreement_id"],
        },
      ],
    }
  );

  AppointmentGuestPatientNdaAgreement.associate = (models) => {
    AppointmentGuestPatientNdaAgreement.belongsTo(models.AppointmentGuest, {
      foreignKey: "appointment_guest_id",
      as: "appointmentGuest",
    });

    AppointmentGuestPatientNdaAgreement.belongsTo(models.PatientNdaAgreement, {
      foreignKey: "patient_nda_agreement_id",
      as: "ndaAgreement",
    });
  };

  history(AppointmentGuestPatientNdaAgreement, sequelize, DataTypes);

  return AppointmentGuestPatientNdaAgreement;
};
